#include "PowerManager.h"

PowerManager::PowerManager() : initialized(false) {
    wakeupReason = esp_sleep_get_wakeup_cause();
}

void PowerManager::begin() {
    initialized = true;
    
    // Print wake-up reason for debugging
    printWakeupReason();
    
    // Configure wake-up source (timer only for now)
    esp_sleep_enable_timer_wakeup(calculateSleepDuration(5, 10));
    
    Serial.println("PowerManager initialized");
}

void PowerManager::enterDeepSleep(uint32_t minMinutes, uint32_t maxMinutes) {
    if (!initialized) {
        Serial.println("PowerManager not initialized!");
        return;
    }
    
    // Calculate sleep duration
    uint64_t sleepDuration = calculateSleepDuration(minMinutes, maxMinutes);
    
    // Convert back to minutes for logging
    uint32_t sleepMinutes = sleepDuration / MINUTE_TO_MICROSECONDS;
    
    Serial.printf("Entering deep sleep for %u minutes...\n", sleepMinutes);
    Serial.flush(); // Ensure all serial output is sent
    
    // Prepare for sleep
    prepareForSleep();
    
    // Configure timer wake-up
    esp_sleep_enable_timer_wakeup(sleepDuration);
    
    // Enter deep sleep
    esp_deep_sleep_start();
}

esp_sleep_wakeup_cause_t PowerManager::getWakeupReason() {
    return wakeupReason;
}

void PowerManager::printWakeupReason() {
    switch (wakeupReason) {
        case ESP_SLEEP_WAKEUP_EXT0:
            Serial.println("Wakeup caused by external signal using RTC_IO");
            break;
        case ESP_SLEEP_WAKEUP_EXT1:
            Serial.println("Wakeup caused by external signal using RTC_CNTL");
            break;
        case ESP_SLEEP_WAKEUP_TIMER:
            Serial.println("Wakeup caused by timer");
            break;
        case ESP_SLEEP_WAKEUP_TOUCHPAD:
            Serial.println("Wakeup caused by touchpad");
            break;
        case ESP_SLEEP_WAKEUP_ULP:
            Serial.println("Wakeup caused by ULP program");
            break;
        default:
            Serial.printf("Wakeup was not caused by deep sleep: %d\n", wakeupReason);
            break;
    }
}

uint64_t PowerManager::calculateSleepDuration(uint32_t minMinutes, uint32_t maxMinutes) {
    if (maxMinutes <= minMinutes) {
        maxMinutes = minMinutes + 1;
    }
    
    // Generate random duration between min and max minutes
    uint32_t rangeMicroseconds = (maxMinutes - minMinutes) * 60 * 1000000;
    uint32_t randomOffset = esp_random() % rangeMicroseconds;
    uint64_t sleepDuration = (minMinutes * MINUTE_TO_MICROSECONDS) + randomOffset;
    
    return sleepDuration;
}

bool PowerManager::isFirstBoot() {
    return (wakeupReason != ESP_SLEEP_WAKEUP_TIMER && 
            wakeupReason != ESP_SLEEP_WAKEUP_EXT0 && 
            wakeupReason != ESP_SLEEP_WAKEUP_EXT1 &&
            wakeupReason != ESP_SLEEP_WAKEUP_TOUCHPAD &&
            wakeupReason != ESP_SLEEP_WAKEUP_ULP);
}

void PowerManager::prepareForSleep() {
    // Flush any remaining serial output
    Serial.flush();
    
    // Small delay to ensure everything is settled
    delay(100);
    
    // Additional cleanup can be added here if needed
    // For example: turning off peripherals, saving final state, etc.
}
