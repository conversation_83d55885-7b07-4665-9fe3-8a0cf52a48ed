#ifndef CONFIG_H
#define CONFIG_H

// WiFi Configuration
#define WIFI_SSID "2208"
#define WIFI_PASSWORD "11223344"

// MQTT Configuration
#define MQTT_SERVER "your-mqtt-broker.com"
#define MQTT_PORT 1883
#define M<PERSON><PERSON>_USER "your_mqtt_user"      // Leave empty if no authentication
#define MQTT_PASSWORD "your_mqtt_pass"  // Leave empty if no authentication

// Sensor Configuration
#define SENSOR_RX_PIN 8
#define SENSOR_TX_PIN 9
#define SENSOR_BAUDRATE 9600

// Power Management Configuration
#define SLEEP_MIN_MINUTES 5     // Minimum sleep time in minutes
#define SLEEP_MAX_MINUTES 10    // Maximum sleep time in minutes

// Reporting Thresholds
#define TDS_THRESHOLD_PPM 1000  // TDS difference threshold in ppm
#define TEMP_THRESHOLD_C 1.0    // Temperature difference threshold in Celsius

// Connection Timeouts
#define WIFI_TIMEOUT_MS 30000   // WiFi connection timeout
#define MQTT_TIMEOUT_MS 10000   // MQTT connection timeout

// Debug Configuration
#define DEBUG_ENABLED true
#define SERIAL_BAUDRATE 115200

// Device Configuration
#define DEVICE_NAME "FishTankSensor"
#define FIRMWARE_VERSION "1.0.0"

// Timing Configuration
#define SENSOR_WARMUP_DELAY_MS 2000

// Memory Management
#define ENABLE_MEMORY_MONITORING true

#endif // CONFIG_H
