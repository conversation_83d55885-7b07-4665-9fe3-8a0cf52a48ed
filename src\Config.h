#ifndef CONFIG_H
#define CONFIG_H

// WiFi Configuration
#define WIFI_SSID "2208"
#define WIFI_PASSWORD "1122334455"

// MQTT Configuration
#define MQTT_SERVER "192.168.10.67"
#define MQTT_PORT 18084
#define MQTT_USER "client1"      // Leave empty if no authentication
#define MQTT_PASSWORD "123"  // Leave empty if no authentication

// Sensor Configuration
#define SENSOR_RX_PIN 8
#define SENSOR_TX_PIN 9
#define SENSOR_BAUDRATE 9600

// Power Management Configuration
#define SLEEP_MIN_SECONDS 30   // Minimum sleep time in seconds
#define SLEEP_MAX_SECONDS 60   // Maximum sleep time in seconds

// Reporting Thresholds
#define TDS_THRESHOLD_PPM 10  // TDS difference threshold in ppm
#define TEMP_THRESHOLD_C 0.5    // Temperature difference threshold in Celsius

// Connection Timeouts
#define WIFI_TIMEOUT_MS 30000   // WiFi connection timeout
#define MQTT_TIMEOUT_MS 10000   // MQTT connection timeout

// Debug Configuration
#define DEBUG_ENABLED true
#define SERIAL_BAUDRATE 115200

// Device Configuration
#define DEVICE_NAME "FishTankSensor"
#define FIRMWARE_VERSION "1.0.0"

// Timing Configuration
#define SENSOR_WARMUP_DELAY_MS 2000

// Memory Management
#define ENABLE_MEMORY_MONITORING true

#endif // CONFIG_H
