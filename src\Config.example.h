#ifndef CONFIG_H
#define CONFIG_H

// WiFi Configuration - CHANGE THESE VALUES
#define WIFI_SSID "YourWiFiNetwork"
#define WIFI_PASSWORD "YourWiFiPassword"

// MQTT Configuration - CHANGE THESE VALUES
#define MQTT_SERVER "*************"  // Your MQTT broker IP or hostname
#define MQTT_PORT 1883
#define MQTT_USER ""                 // Leave empty if no authentication
#define MQTT_PASSWORD ""             // Leave empty if no authentication

// Sensor Configuration
#define SENSOR_RX_PIN 8
#define SENSOR_TX_PIN 9
#define SENSOR_BAUDRATE 9600

// Power Management Configuration
#define SLEEP_MIN_MINUTES 5     // Minimum sleep time in minutes
#define SLEEP_MAX_MINUTES 10    // Maximum sleep time in minutes

// Reporting Thresholds
#define TDS_THRESHOLD_PPM 1000  // TDS difference threshold in ppm
#define TEMP_THRESHOLD_C 1.0    // Temperature difference threshold in Celsius

// Connection Timeouts
#define WIFI_TIMEOUT_MS 30000   // WiFi connection timeout
#define MQTT_TIMEOUT_MS 10000   // MQTT connection timeout

// Debug Configuration
#define DEBUG_ENABLED true
#define SERIAL_BAUDRATE 115200

// Device Configuration
#define DEVICE_NAME "FishTankSensor"
#define FIRMWARE_VERSION "1.0.0"

// Timing Configuration
#define SENSOR_READING_TIMEOUT_MS 5000
#define CONNECTION_RETRY_DELAY_MS 1000
#define SENSOR_WARMUP_DELAY_MS 2000

// Daily Report Configuration
#define DAILY_REPORT_ENABLED true
#define DAILY_REPORT_RANDOM_OFFSET true  // If true, daily reports are at random times

// Error Handling
#define MAX_CONNECTION_RETRIES 3
#define MAX_SENSOR_READ_RETRIES 3

// Memory Management
#define ENABLE_MEMORY_MONITORING true

#endif // CONFIG_H
