# 蓝牙配网指南 (Bluetooth Configuration Guide)

## 🔵 功能概述

鱼缸传感器现在支持通过蓝牙进行WiFi和MQTT配置，无需硬编码网络信息。

### 📋 配网逻辑

1. **首次上电**：
   - 如果没有保存的WiFi配置，自动进入蓝牙配网模式
   - 配网超时时间：3分钟
   - 超时后进入永久深度睡眠

2. **深度睡眠唤醒**：
   - 尝试连接已保存的WiFi网络
   - 连续3次连接失败后进入永久深度睡眠
   - 成功连接后重置失败计数

## 📱 配网步骤

### 1. 设备准备
1. 上传固件到ESP32-S3
2. 设备会自动进入蓝牙配网模式
3. 观察串口输出确认设备名称

### 2. 蓝牙连接
设备蓝牙名称格式：`FishTank_XXXXXX` (XXXXXX为MAC地址后6位)

**Android设备：**
1. 打开蓝牙设置
2. 搜索并连接到 `FishTank_XXXXXX`
3. 使用蓝牙串口应用 (如 Serial Bluetooth Terminal)

**iOS设备：**
1. 使用支持蓝牙串口的应用 (如 LightBlue)
2. 连接到设备并找到串口服务

**电脑：**
1. 配对蓝牙设备
2. 使用串口终端软件连接

### 3. 发送配置
连接成功后，设备会发送配置指令：

```json
{
  "device": "FishTank_A1B2C3",
  "status": "ready",
  "message": "Send WiFi and MQTT configuration",
  "format": "{\"ssid\":\"WiFi_Name\",\"password\":\"WiFi_Pass\",\"mqtt_server\":\"*************\",\"mqtt_port\":1883,\"mqtt_user\":\"user\",\"mqtt_password\":\"pass\"}"
}
```

### 4. 配置格式
发送以下JSON格式的配置信息：

```json
{
  "ssid": "你的WiFi名称",
  "password": "你的WiFi密码",
  "mqtt_server": "MQTT服务器地址",
  "mqtt_port": 1883,
  "mqtt_user": "MQTT用户名",
  "mqtt_password": "MQTT密码"
}
```

**必填字段：**
- `ssid`: WiFi网络名称
- `password`: WiFi密码
- `mqtt_server`: MQTT服务器IP或域名
- `mqtt_port`: MQTT端口号

**可选字段：**
- `mqtt_user`: MQTT用户名 (可为空)
- `mqtt_password`: MQTT密码 (可为空)

### 5. 配置确认
发送配置后，设备会返回确认信息：

**成功：**
```json
{
  "status": "success",
  "message": "Configuration saved successfully!"
}
```

**失败：**
```json
{
  "status": "error",
  "message": "Invalid configuration format"
}
```

## 🔧 配置示例

### 示例1：基本配置
```json
{
  "ssid": "MyHome_WiFi",
  "password": "mypassword123",
  "mqtt_server": "*************",
  "mqtt_port": 1883,
  "mqtt_user": "",
  "mqtt_password": ""
}
```

### 示例2：带认证的MQTT
```json
{
  "ssid": "Office_Network",
  "password": "office2024",
  "mqtt_server": "mqtt.example.com",
  "mqtt_port": 8883,
  "mqtt_user": "sensor_user",
  "mqtt_password": "sensor_pass"
}
```

## 📊 状态指示

### 串口输出信息

**配网模式启动：**
```
=== BLUETOOTH CONFIGURATION MODE ===
Device name: FishTank_A1B2C3
Waiting for configuration via Bluetooth...
Bluetooth config mode active for 180 seconds...
```

**配置成功：**
```
Configuration received successfully!
WiFi connection successful with new credentials!
```

**配置失败：**
```
Bluetooth configuration timeout or failed.
=== ENTERING PERMANENT SLEEP MODE ===
```

## ⚠️ 故障排除

### 问题1：无法发现蓝牙设备
**可能原因：**
- 设备未进入配网模式
- 蓝牙功能未启用
- 距离太远

**解决方法：**
- 重启设备确保进入配网模式
- 检查手机蓝牙是否开启
- 靠近设备 (1-2米内)

### 问题2：连接后无响应
**可能原因：**
- 蓝牙串口应用问题
- 设备配网超时

**解决方法：**
- 尝试不同的蓝牙串口应用
- 重启设备重新配网
- 检查串口输出确认状态

### 问题3：配置发送失败
**可能原因：**
- JSON格式错误
- 必填字段缺失
- 特殊字符问题

**解决方法：**
- 验证JSON格式正确性
- 确保所有必填字段存在
- 避免使用特殊字符

### 问题4：WiFi连接失败
**可能原因：**
- WiFi密码错误
- 网络信号弱
- 网络不支持2.4GHz

**解决方法：**
- 确认WiFi密码正确
- 移动设备到路由器附近
- 确保使用2.4GHz网络

## 🔄 重新配网

如需重新配网：

1. **清除配置：**
   - 在代码中临时添加 `btConfig.clearCredentials();`
   - 或者擦除设备Flash

2. **强制配网模式：**
   - 修改代码跳过凭据检查
   - 重新上传固件

## 📱 推荐应用

### Android
- **Serial Bluetooth Terminal** (免费)
- **Bluetooth Terminal** (免费)
- **BlueTerm** (免费)

### iOS
- **LightBlue** (免费)
- **BLE Scanner** (免费)
- **nRF Connect** (免费)

### Windows/Mac/Linux
- **PuTTY** (Windows)
- **Screen** (Mac/Linux)
- **Minicom** (Linux)

## 🔒 安全注意事项

1. **配网期间：**
   - 确保在安全环境中进行配网
   - 避免在公共场所暴露WiFi密码

2. **存储安全：**
   - 凭据加密存储在NVS中
   - 设备重置会清除所有凭据

3. **网络安全：**
   - 使用强WiFi密码
   - 定期更换MQTT认证信息

配网完成后，设备会自动连接WiFi并开始正常的传感器数据采集和上报功能。
