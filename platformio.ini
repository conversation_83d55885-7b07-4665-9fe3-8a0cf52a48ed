; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:adafruit_feather_esp32s3]
platform = espressif32
board = adafruit_feather_esp32s3
framework = arduino
lib_deps =
    knolleary/PubSubClient@^2.8
    bblanchon/<PERSON>rd<PERSON><PERSON><PERSON><PERSON>@^7.0.4
monitor_speed = 115200
build_flags =
    -DCORE_DEBUG_LEVEL=3
board_build.partitions = huge_app.csv
board_build.arduino.memory_type = qio_opi
