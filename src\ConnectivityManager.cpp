#include "ConnectivityManager.h"

ConnectivityManager::ConnectivityManager() : 
    mqttClient(wifiClient), 
    mqttPort(1883),
    initialized(false),
    wifiConnected(false),
    mqttConnected(false) {
}

ConnectivityManager::~ConnectivityManager() {
    disconnect();
}

bool ConnectivityManager::begin(const char* ssid, const char* password, 
                               const char* mqttServer, int mqttPort, 
                               const char* mqttUser, const char* mqttPassword) {
    
    wifiSSID = ssid;
    wifiPassword = password;
    this->mqttServer = mqttServer;
    this->mqttPort = mqttPort;
    
    if (mqttUser) this->mqttUser = mqttUser;
    if (mqttPassword) this->mqttPassword = mqttPassword;
    
    generateDeviceId();
    setupTopics();
    
    // Setup MQTT client
    mqttClient.setServer(mqttServer, mqttPort);
    mqttClient.setCallback(mqttCallback);
    
    initialized = true;
    
    Serial.printf("ConnectivityManager initialized for device: %s\n", deviceId.c_str());
    Serial.printf("MQTT Server: %s:%d\n", mqttServer, mqttPort);
    
    return true;
}

bool ConnectivityManager::connectWiFi(uint32_t timeoutMs) {
    if (wifiConnected) {
        return true;
    }
    
    Serial.printf("Connecting to WiFi: %s", wifiSSID.c_str());
    
    WiFi.mode(WIFI_STA);
    WiFi.begin(wifiSSID.c_str(), wifiPassword.c_str());
    
    uint32_t startTime = millis();
    while (WiFi.status() != WL_CONNECTED && (millis() - startTime) < timeoutMs) {
        delay(WIFI_RETRY_INTERVAL);
        Serial.print(".");
    }
    
    if (WiFi.status() == WL_CONNECTED) {
        wifiConnected = true;
        onWiFiConnected();
        return true;
    } else {
        Serial.println("\nWiFi connection failed!");
        return false;
    }
}

bool ConnectivityManager::connectMQTT(uint32_t timeoutMs) {
    if (!wifiConnected) {
        Serial.println("WiFi not connected, cannot connect to MQTT");
        return false;
    }
    
    if (mqttConnected && mqttClient.connected()) {
        return true;
    }
    
    Serial.printf("Connecting to MQTT broker: %s:%d", mqttServer.c_str(), mqttPort);
    
    uint32_t startTime = millis();
    while (!mqttClient.connected() && (millis() - startTime) < timeoutMs) {
        bool connected;
        
        if (mqttUser.length() > 0) {
            connected = mqttClient.connect(clientId.c_str(), mqttUser.c_str(), mqttPassword.c_str());
        } else {
            connected = mqttClient.connect(clientId.c_str());
        }
        
        if (connected) {
            mqttConnected = true;
            Serial.println("\nMQTT connected!");
            
            // Publish connection status
            // publishStatus(0, "connected");
            
            return true;
        } else {
            Serial.printf(".");
            delay(MQTT_RETRY_INTERVAL);
        }
    }
    
    Serial.printf("\nMQTT connection failed! State: %d\n", mqttClient.state());
    return false;
}

void ConnectivityManager::disconnect() {
    if (mqttConnected) {
        mqttClient.disconnect();
        mqttConnected = false;
    }
    
    if (wifiConnected) {
        WiFi.disconnect();
        wifiConnected = false;
    }
    
    Serial.println("Disconnected from WiFi and MQTT");
}

bool ConnectivityManager::isConnected() {
    return wifiConnected && mqttConnected && mqttClient.connected();
}

bool ConnectivityManager::publishSensorData(const SensorReading& reading, bool isDailyReport) {
    if (!isConnected()) {
        Serial.println("Not connected, cannot publish sensor data");
        return false;
    }
    
    String payload = createSensorDataPayload(reading, isDailyReport);
    
    bool success = mqttClient.publish(topicSensorData.c_str(), payload.c_str(), true); // retained message
    
    if (success) {
        Serial.printf("Published sensor data: %s\n", payload.c_str());
    } else {
        Serial.println("Failed to publish sensor data");
    }
    
    return success;
}

bool ConnectivityManager::publishStatus(uint32_t bootCount, const char* status) {
    if (!isConnected()) {
        return false;
    }
    
    String payload = createStatusPayload(bootCount, status);
    
    bool success = mqttClient.publish(topicStatus.c_str(), payload.c_str());
    
    if (success) {
        Serial.printf("Published status: %s\n", payload.c_str());
    }
    
    return success;
}

void ConnectivityManager::loop() {
    if (mqttConnected) {
        mqttClient.loop();
    }
}

String ConnectivityManager::getConnectionStatus() {
    String status = "WiFi: ";
    status += wifiConnected ? "Connected" : "Disconnected";
    status += ", MQTT: ";
    status += mqttConnected ? "Connected" : "Disconnected";
    
    if (wifiConnected) {
        status += " (IP: " + WiFi.localIP().toString() + ")";
    }
    
    return status;
}

void ConnectivityManager::generateDeviceId() {
    uint8_t mac[6];
    WiFi.macAddress(mac);
    
    deviceId = "fish-tank-";
    for (int i = 0; i < 6; i++) {
        if (mac[i] < 16) deviceId += "0";
        deviceId += String(mac[i], HEX);
    }
    
    clientId = deviceId + "-" + String(millis());
}

void ConnectivityManager::setupTopics() {
    String baseTopic = "fishtank/" + deviceId;
    topicSensorData = baseTopic + "/sensor";
    topicStatus = baseTopic + "/status";
    topicHeartbeat = baseTopic + "/heartbeat";
}

void ConnectivityManager::mqttCallback(char* topic, byte* payload, unsigned int length) {
    // For future use if we need to handle incoming MQTT messages
    Serial.printf("MQTT message received on topic: %s\n", topic);
}

String ConnectivityManager::createSensorDataPayload(const SensorReading& reading, bool isDailyReport) {
    JsonDocument doc;
    
    doc["device_id"] = deviceId;
    doc["timestamp"] = reading.timestamp;
    doc["tds"] = reading.tds;
    doc["temperature"] = reading.temperature;
    doc["daily_report"] = isDailyReport;
    doc["uptime"] = millis();
    
    String payload;
    serializeJson(doc, payload);
    return payload;
}

String ConnectivityManager::createStatusPayload(uint32_t bootCount, const char* status) {
    JsonDocument doc;
    
    doc["device_id"] = deviceId;
    doc["status"] = status;
    doc["boot_count"] = bootCount;
    doc["uptime"] = millis();
    doc["free_heap"] = ESP.getFreeHeap();
    doc["wifi_rssi"] = WiFi.RSSI();
    
    String payload;
    serializeJson(doc, payload);
    return payload;
}

void ConnectivityManager::onWiFiConnected() {
    Serial.println();
    Serial.printf("WiFi connected! IP: %s\n", WiFi.localIP().toString().c_str());
    Serial.printf("Signal strength: %d dBm\n", WiFi.RSSI());
}

void ConnectivityManager::onWiFiDisconnected() {
    wifiConnected = false;
    mqttConnected = false;
    Serial.println("WiFi disconnected");
}
