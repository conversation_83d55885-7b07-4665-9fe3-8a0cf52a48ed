# Fish Tank Sensor System

A low-power ESP32-S3 based fish tank monitoring system that measures TDS (Total Dissolved Solids) and temperature, with intelligent MQTT reporting and deep sleep power management.

## Features

- **Smart Data Collection**: Random sampling every 5-10 minutes with deep sleep between readings
- **Intelligent Reporting**: Reports data via MQTT when:
  - TDS changes by more than 1000 ppm from last reading
  - Temperature changes by more than 1°C from last reading
  - Daily report (at random time each day)
- **Ultra Low Power**: Deep sleep mode between readings for maximum battery life
- **Persistent Storage**: Uses NVS to maintain state across deep sleep cycles
- **Robust Connectivity**: WiFi and MQTT with automatic reconnection
- **Comprehensive Logging**: Detailed debug output and system statistics

## Hardware Requirements

- ESP32-S3 development board (tested with Adafruit Feather ESP32-S3)
- TDS sensor with UART interface
- WiFi network access
- MQTT broker

## Wiring

| ESP32-S3 Pin | TDS Sensor |
|--------------|------------|
| GPIO 8       | RX         |
| GPIO 9       | TX         |
| 3.3V         | VCC        |
| GND          | GND        |

## Software Setup

### 1. Configure WiFi and MQTT

Edit `src/Config.h` and update the following settings:

```cpp
// WiFi Configuration
#define WIFI_SSID "YOUR_WIFI_SSID"
#define WIFI_PASSWORD "YOUR_WIFI_PASSWORD"

// MQTT Configuration
#define MQTT_SERVER "your-mqtt-broker.com"
#define MQTT_PORT 1883
#define MQTT_USER "your_mqtt_user"      // Leave empty if no authentication
#define MQTT_PASSWORD "your_mqtt_pass"  // Leave empty if no authentication
```

### 2. Install Dependencies

The required libraries are automatically installed via PlatformIO:
- PubSubClient (MQTT)
- ArduinoJson (JSON handling)

### 3. Build and Upload

```bash
pio run --target upload
```

### 4. Monitor Serial Output

```bash
pio device monitor
```

## MQTT Topics

The system publishes to the following topics:

- `fishtank/{device_id}/sensor` - Sensor data
- `fishtank/{device_id}/status` - Device status
- `fishtank/{device_id}/heartbeat` - Heartbeat messages

### Sensor Data Format

```json
{
  "device_id": "fish-tank-aabbccddeeff",
  "timestamp": 1234567890,
  "tds": 450,
  "temperature": 24.5,
  "daily_report": false,
  "uptime": 15000
}
```

### Status Data Format

```json
{
  "device_id": "fish-tank-aabbccddeeff",
  "status": "online",
  "boot_count": 15,
  "uptime": 15000,
  "free_heap": 200000,
  "wifi_rssi": -45
}
```

## Configuration Options

Key configuration parameters in `src/Config.h`:

| Parameter | Default | Description |
|-----------|---------|-------------|
| `SLEEP_MIN_MINUTES` | 5 | Minimum sleep time between readings |
| `SLEEP_MAX_MINUTES` | 10 | Maximum sleep time between readings |
| `TDS_THRESHOLD_PPM` | 1000 | TDS change threshold for reporting |
| `TEMP_THRESHOLD_C` | 1.0 | Temperature change threshold for reporting |
| `WIFI_TIMEOUT_MS` | 30000 | WiFi connection timeout |
| `MQTT_TIMEOUT_MS` | 10000 | MQTT connection timeout |

## Power Consumption

The system is designed for ultra-low power operation:

- **Active time**: ~30-60 seconds per cycle (sensor reading + potential reporting)
- **Sleep time**: 5-10 minutes (configurable)
- **Deep sleep current**: ~10-50 µA (depending on ESP32-S3 variant)
- **Estimated battery life**: Several months on a single 18650 battery

## Troubleshooting

### Common Issues

1. **Sensor not responding**
   - Check wiring connections
   - Verify sensor power supply
   - Check serial communication settings

2. **WiFi connection fails**
   - Verify SSID and password in Config.h
   - Check WiFi signal strength
   - Ensure 2.4GHz network (ESP32 doesn't support 5GHz)

3. **MQTT connection fails**
   - Verify broker address and port
   - Check authentication credentials
   - Ensure broker is accessible from your network

4. **Frequent reboots**
   - Check power supply stability
   - Monitor serial output for error messages
   - Verify all connections are secure

### Debug Mode

Enable detailed debugging by setting in `src/Config.h`:

```cpp
#define DEBUG_ENABLED true
```

### Reset System Data

To clear all stored data (useful for testing):

```cpp
// In setup(), add temporarily:
dataManager.clearAllData();
```

## System Architecture

The system consists of several modular components:

- **SensorDataManager**: Handles sensor data storage and threshold checking
- **PowerManager**: Manages deep sleep and wake-up cycles
- **ConnectivityManager**: Handles WiFi and MQTT connections
- **ReportingLogic**: Determines when and what to report
- **Config.h**: Centralized configuration

## License

This project is open source. Feel free to modify and distribute according to your needs.

## Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.
