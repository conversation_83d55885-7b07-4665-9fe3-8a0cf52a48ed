#include <Arduino.h>
#include <BluetoothSerial.h>

BluetoothSerial SerialBT;

void setup() {
  Serial.begin(115200);
  Serial.println("Starting Bluetooth test...");
  
  if (!SerialBT.begin("FishTank_Test")) {
    Serial.println("An error occurred initializing Bluetooth");
  } else {
    Serial.println("Bluetooth initialized successfully");
    Serial.println("Device name: FishTank_Test");
  }
}

void loop() {
  if (SerialBT.available()) {
    String message = SerialBT.readString();
    Serial.printf("Received: %s\n", message.c_str());
    SerialBT.println("Echo: " + message);
  }
  
  if (Serial.available()) {
    String message = Serial.readString();
    SerialBT.println("From Serial: " + message);
  }
  
  delay(100);
}
