#!/usr/bin/env python3
"""
蓝牙配网测试脚本
Bluetooth Configuration Test Script

使用方法:
1. 确保设备进入蓝牙配网模式
2. 修改下面的配置信息
3. 运行脚本: python bluetooth_config_test.py

需要安装: pip install pybluez
"""

import json
import time
import sys

# 配置信息 - 请根据实际情况修改
CONFIG = {
    "ssid": "YourWiFiName",
    "password": "YourWiFiPassword", 
    "mqtt_server": "*************",
    "mqtt_port": 1883,
    "mqtt_user": "mqtt_user",
    "mqtt_password": "mqtt_pass"
}

# 设备蓝牙名称前缀
DEVICE_NAME_PREFIX = "FishTank_"

def find_fishtank_device():
    """查找鱼缸传感器设备"""
    try:
        import bluetooth
        print("正在搜索蓝牙设备...")
        
        devices = bluetooth.discover_devices(duration=10, lookup_names=True)
        
        for addr, name in devices:
            if name and name.startswith(DEVICE_NAME_PREFIX):
                print(f"找到设备: {name} ({addr})")
                return addr, name
                
        print("未找到鱼缸传感器设备")
        return None, None
        
    except ImportError:
        print("错误: 需要安装 pybluez 库")
        print("安装命令: pip install pybluez")
        return None, None
    except Exception as e:
        print(f"蓝牙搜索错误: {e}")
        return None, None

def send_config(addr, config):
    """发送配置到设备"""
    try:
        import bluetooth
        
        print(f"连接到设备: {addr}")
        sock = bluetooth.BluetoothSocket(bluetooth.RFCOMM)
        sock.connect((addr, 1))  # RFCOMM channel 1
        
        print("连接成功，等待设备响应...")
        time.sleep(2)
        
        # 接收设备的初始消息
        try:
            response = sock.recv(1024).decode('utf-8')
            print(f"设备响应: {response}")
        except:
            pass
        
        # 发送配置
        config_json = json.dumps(config)
        print(f"发送配置: {config_json}")
        
        sock.send((config_json + '\n').encode('utf-8'))
        
        # 等待确认响应
        print("等待配置确认...")
        time.sleep(3)
        
        try:
            response = sock.recv(1024).decode('utf-8')
            print(f"配置响应: {response}")
            
            # 解析响应
            try:
                resp_data = json.loads(response)
                if resp_data.get('status') == 'success':
                    print("✓ 配置成功!")
                    return True
                else:
                    print(f"✗ 配置失败: {resp_data.get('message', '未知错误')}")
                    return False
            except:
                print("响应格式无法解析")
                return False
                
        except Exception as e:
            print(f"接收响应错误: {e}")
            return False
            
    except Exception as e:
        print(f"连接错误: {e}")
        return False
    finally:
        try:
            sock.close()
        except:
            pass

def main():
    print("=== 鱼缸传感器蓝牙配网测试 ===")
    print()
    
    # 显示当前配置
    print("当前配置:")
    for key, value in CONFIG.items():
        if 'password' in key.lower():
            print(f"  {key}: {'*' * len(str(value))}")
        else:
            print(f"  {key}: {value}")
    print()
    
    # 确认配置
    confirm = input("确认使用此配置? (y/N): ").strip().lower()
    if confirm != 'y':
        print("请修改脚本中的CONFIG变量后重新运行")
        return
    
    # 搜索设备
    addr, name = find_fishtank_device()
    if not addr:
        print("未找到设备，请确保:")
        print("1. 设备已进入蓝牙配网模式")
        print("2. 设备在蓝牙范围内")
        print("3. 蓝牙功能已启用")
        return
    
    # 发送配置
    print()
    success = send_config(addr, CONFIG)
    
    if success:
        print()
        print("🎉 配网完成!")
        print("设备将自动连接WiFi并开始工作")
    else:
        print()
        print("❌ 配网失败")
        print("请检查配置信息并重试")

if __name__ == "__main__":
    main()
