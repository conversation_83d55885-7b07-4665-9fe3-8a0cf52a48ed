#ifndef POWER_MANAGER_H
#define POWER_MANAGER_H

#include <Arduino.h>
#include <esp_sleep.h>
#include <esp_random.h>

class PowerManager {
public:
    PowerManager();
    
    // Initialize power management
    void begin();
    
    // Enter deep sleep for a random interval between min and max minutes
    void enterDeepSleep(uint32_t minMinutes = 5, uint32_t maxMinutes = 10);
    
    // Get the wake-up reason
    esp_sleep_wakeup_cause_t getWakeupReason();
    
    // Print wake-up reason for debugging
    void printWakeupReason();
    
    // Calculate next sleep duration in microseconds
    uint64_t calculateSleepDuration(uint32_t minMinutes, uint32_t maxMinutes);
    
    // Check if this is the first boot (not a wake-up from deep sleep)
    bool isFirstBoot();
    
    // Prepare for deep sleep (cleanup, save state, etc.)
    void prepareForSleep();

private:
    esp_sleep_wakeup_cause_t wakeupReason;
    bool initialized;
    
    // Convert minutes to microseconds for ESP32 sleep timer
    static const uint64_t MINUTE_TO_MICROSECONDS = 60ULL * 1000000ULL;
};

#endif // POWER_MANAGER_H
