#include "SerialConfigManager.h"

// Static constants
const char* SerialConfigManager::NVS_NAMESPACE = "serial_config";
const char* SerialConfigManager::KEY_WIFI_SSID = "wifi_ssid";
const char* SerialConfigManager::KEY_WIFI_PASSWORD = "wifi_pass";
const char* SerialConfigManager::KEY_MQTT_SERVER = "mqtt_server";
const char* SerialConfigManager::KEY_MQTT_PORT = "mqtt_port";
const char* SerialConfigManager::KEY_MQTT_USER = "mqtt_user";
const char* SerialConfigManager::KEY_MQTT_PASSWORD = "mqtt_pass";
const char* SerialConfigManager::KEY_CONFIG_VALID = "config_valid";

SerialConfigManager::SerialConfigManager() : initialized(false) {
}

SerialConfigManager::~SerialConfigManager() {
}

bool SerialConfigManager::begin() {
    if (!preferences.begin(NVS_NAMESPACE, false)) {
        Serial.println("Failed to initialize Serial config NVS");
        return false;
    }
    
    initialized = true;
    Serial.println("SerialConfigManager initialized");
    return true;
}

WiFiCredentials SerialConfigManager::loadCredentials() {
    WiFiCredentials creds;
    
    if (!initialized) return creds;
    
    bool configValid = preferences.getBool(KEY_CONFIG_VALID, false);
    if (!configValid) {
        Serial.println("No valid configuration found");
        return creds;
    }
    
    creds.ssid = preferences.getString(KEY_WIFI_SSID, "");
    creds.password = preferences.getString(KEY_WIFI_PASSWORD, "");
    creds.mqttServer = preferences.getString(KEY_MQTT_SERVER, "");
    creds.mqttPort = preferences.getInt(KEY_MQTT_PORT, 1883);
    creds.mqttUser = preferences.getString(KEY_MQTT_USER, "");
    creds.mqttPassword = preferences.getString(KEY_MQTT_PASSWORD, "");
    
    if (creds.ssid.length() > 0 && creds.mqttServer.length() > 0) {
        creds.valid = true;
        Serial.println("Loaded valid credentials from NVS");
    }
    
    return creds;
}

bool SerialConfigManager::saveCredentials(const WiFiCredentials& creds) {
    if (!initialized || !validateCredentials(creds)) {
        return false;
    }
    
    preferences.putString(KEY_WIFI_SSID, creds.ssid);
    preferences.putString(KEY_WIFI_PASSWORD, creds.password);
    preferences.putString(KEY_MQTT_SERVER, creds.mqttServer);
    preferences.putInt(KEY_MQTT_PORT, creds.mqttPort);
    preferences.putString(KEY_MQTT_USER, creds.mqttUser);
    preferences.putString(KEY_MQTT_PASSWORD, creds.mqttPassword);
    preferences.putBool(KEY_CONFIG_VALID, true);
    
    Serial.println("Credentials saved to NVS");
    return true;
}

void SerialConfigManager::clearCredentials() {
    if (!initialized) return;
    
    preferences.clear();
    Serial.println("All credentials cleared");
}

bool SerialConfigManager::startConfigMode(uint32_t timeoutMs) {
    if (!initialized) {
        Serial.println("SerialConfigManager not initialized");
        return false;
    }
    
    Serial.println("=== SERIAL CONFIGURATION MODE ===");
    sendInstructions();
    
    unsigned long startTime = millis();
    String inputBuffer = "";
    
    Serial.printf("Serial config mode active for %d seconds...\n", timeoutMs / 1000);
    
    while (millis() - startTime < timeoutMs) {
        if (Serial.available()) {
            char c = Serial.read();
            
            if (c == '\n' || c == '\r') {
                if (inputBuffer.length() > 0) {
                    Serial.printf("Received: %s\n", inputBuffer.c_str());
                    
                    if (handleConfigData(inputBuffer)) {
                        sendResponse("Configuration saved successfully!");
                        return true;
                    }
                    inputBuffer = "";
                }
            } else {
                inputBuffer += c;
            }
        }
        
        // Show progress every 30 seconds
        if ((millis() - startTime) % 30000 == 0) {
            uint32_t remaining = (timeoutMs - (millis() - startTime)) / 1000;
            Serial.printf("Config mode timeout in %d seconds\n", remaining);
        }
        
        delay(100);
    }
    
    Serial.println("Serial config mode timeout");
    return false;
}

bool SerialConfigManager::hasValidCredentials() {
    WiFiCredentials creds = loadCredentials();
    return creds.valid;
}

bool SerialConfigManager::handleConfigData(const String& data) {
    WiFiCredentials creds;
    
    if (parseConfigJson(data, creds)) {
        return saveCredentials(creds);
    }
    
    sendResponse("Invalid configuration format", false);
    return false;
}

bool SerialConfigManager::parseConfigJson(const String& json, WiFiCredentials& creds) {
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, json);
    
    if (error) {
        Serial.printf("JSON parse error: %s\n", error.c_str());
        return false;
    }
    
    creds.ssid = doc["ssid"].as<String>();
    creds.password = doc["password"].as<String>();
    creds.mqttServer = doc["mqtt_server"].as<String>();
    creds.mqttPort = doc["mqtt_port"] | 1883;
    creds.mqttUser = doc["mqtt_user"].as<String>();
    creds.mqttPassword = doc["mqtt_password"].as<String>();
    
    return validateCredentials(creds);
}

void SerialConfigManager::sendResponse(const String& message, bool success) {
    JsonDocument doc;
    doc["status"] = success ? "success" : "error";
    doc["message"] = message;
    
    String response;
    serializeJson(doc, response);
    
    Serial.println(response);
}

void SerialConfigManager::sendInstructions() {
    JsonDocument doc;
    doc["device"] = "FishTank_Serial";
    doc["status"] = "ready";
    doc["message"] = "Send WiFi and MQTT configuration via Serial";
    doc["format"] = "{\"ssid\":\"WiFi_Name\",\"password\":\"WiFi_Pass\",\"mqtt_server\":\"192.168.1.100\",\"mqtt_port\":1883,\"mqtt_user\":\"user\",\"mqtt_password\":\"pass\"}";
    
    String instructions;
    serializeJson(doc, instructions);
    
    Serial.println(instructions);
}

bool SerialConfigManager::validateCredentials(const WiFiCredentials& creds) {
    if (creds.ssid.length() == 0) {
        Serial.println("SSID cannot be empty");
        return false;
    }
    
    if (creds.mqttServer.length() == 0) {
        Serial.println("MQTT server cannot be empty");
        return false;
    }
    
    if (creds.mqttPort < 1 || creds.mqttPort > 65535) {
        Serial.println("Invalid MQTT port");
        return false;
    }
    
    return true;
}
