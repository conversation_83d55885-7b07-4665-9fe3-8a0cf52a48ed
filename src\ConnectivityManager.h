#ifndef CONNECTIVITY_MANAGER_H
#define CONNECTIVITY_MANAGER_H

#include <Arduino.h>
#include <WiFi.h>
#include <PubSubClient.h>
#include <ArduinoJson.h>
#include "SensorDataManager.h"

// Forward declaration
struct WiFiCredentials;

class ConnectivityManager {
public:
    ConnectivityManager();
    ~ConnectivityManager();
    
    // Initialize WiFi and MQTT
    bool begin(const char* ssid, const char* password,
               const char* mqttServer, int mqttPort,
               const char* mqttUser = nullptr, const char* mqttPassword = nullptr);

    // Initialize with WiFiCredentials structure
    bool begin(const WiFiCredentials& credentials);
    
    // Connect to WiFi
    bool connectWiFi(uint32_t timeoutMs = 30000);
    
    // Connect to MQTT broker
    bool connectMQTT(uint32_t timeoutMs = 10000);
    
    // Disconnect and cleanup
    void disconnect();
    
    // Check if connected to both WiFi and MQTT
    bool isConnected();
    
    // Publish sensor data
    bool publishSensorData(const SensorReading& reading, bool isDailyReport = false);
    
    // Publish status/heartbeat
    bool publishStatus(uint32_t bootCount, const char* status = "online");
    
    // Handle MQTT loop (call regularly if staying connected)
    void loop();
    
    // Get connection status string
    String getConnectionStatus();

private:
    WiFiClient wifiClient;
    PubSubClient mqttClient;
    
    // Connection parameters
    String wifiSSID;
    String wifiPassword;
    String mqttServer;
    int mqttPort;
    String mqttUser;
    String mqttPassword;
    
    // Device identification
    String deviceId;
    String clientId;
    
    // MQTT topics
    String topicSensorData;
    String topicStatus;
    String topicHeartbeat;
    
    bool initialized;
    bool wifiConnected;
    bool mqttConnected;
    
    // Connection timeouts
    static const uint32_t WIFI_RETRY_INTERVAL = 500;
    static const uint32_t MQTT_RETRY_INTERVAL = 1000;
    
    // Generate device ID based on MAC address
    void generateDeviceId();
    
    // Setup MQTT topics
    void setupTopics();
    
    // MQTT callback (for future use if needed)
    static void mqttCallback(char* topic, byte* payload, unsigned int length);
    
    // Create JSON payload for sensor data
    String createSensorDataPayload(const SensorReading& reading, bool isDailyReport);
    
    // Create JSON payload for status
    String createStatusPayload(uint32_t bootCount, const char* status);

    // WiFi event handlers
    void onWiFiConnected();
    void onWiFiDisconnected();
};

#endif // CONNECTIVITY_MANAGER_H
