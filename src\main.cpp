#include <Arduino.h>
#include "Config.h"
#include "TDS_Sensor_UART.h"
#include "SensorDataManager.h"
#include "PowerManager.h"
#include "ConnectivityManager.h"
#include "ReportingLogic.h"
#include "BluetoothConfigManager.h"

// Hardware configuration
HardwareSerial& SensorSerial = Serial1;
TDS_Sensor_UART tds_sensor(SensorSerial);

// System components
SensorDataManager dataManager;
PowerManager powerManager;
ConnectivityManager connectivity;
ReportingLogic reporting(dataManager, connectivity);
BluetoothConfigManager btConfig;

// Global state
bool systemInitialized = false;
unsigned long lastSensorRead = 0;
int wifiFailureCount = 0;

// Function declarations
void performSensorCycle();
SensorReading readSensorData();
void printSystemStatus(const SensorReading& reading);
void enterErrorSleep();
void enterPermanentSleep();
void printMemoryInfo();
void printSystemInfo();
bool initializeWiFiConnection();
bool handleBluetoothConfig();

void setup() {
  // Initialize serial communication
  Serial.begin(SERIAL_BAUDRATE);
  while (!Serial && millis() < 5000); // Wait up to 5 seconds for serial

  Serial.println("\n=== Fish Tank Sensor System ===");
  Serial.printf("Firmware Version: %s\n", FIRMWARE_VERSION);
  Serial.printf("Device: %s\n", DEVICE_NAME);

  // Initialize power manager first to check wake-up reason
  powerManager.begin();

  // Initialize sensor data manager
  if (!dataManager.begin()) {
    Serial.println("Failed to initialize data manager!");
    enterErrorSleep();
    return;
  }

  // Initialize Bluetooth config manager
  if (!btConfig.begin()) {
    Serial.println("Failed to initialize Bluetooth config manager!");
    enterErrorSleep();
    return;
  }

  // Initialize sensor
  tds_sensor.begin(SENSOR_BAUDRATE, SERIAL_8N1, SENSOR_RX_PIN, SENSOR_TX_PIN);
  Serial.printf("Sensor initialized on RX=%d, TX=%d, Baud=%ld\n",
                SENSOR_RX_PIN, SENSOR_TX_PIN, SENSOR_BAUDRATE);

  // Handle WiFi connection and configuration
  if (!initializeWiFiConnection()) {
    Serial.println("Failed to initialize WiFi connection!");
    enterPermanentSleep();
    return;
  }

  // Initialize reporting logic
  reporting.begin();

  systemInitialized = true;

  Serial.printf("System initialized. Boot count: %u\n", dataManager.getBootCount());

  if (powerManager.isFirstBoot()) {
    Serial.println("First boot detected");
    printSystemInfo();
  } else {
    Serial.println("Wake-up from deep sleep");
  }

  if (DEBUG_ENABLED) {
    printMemoryInfo();
  }
}

void loop() {
  if (!systemInitialized) {
    enterErrorSleep();
    return;
  }

  // Main sensor reading and reporting cycle
  performSensorCycle();

  // Enter deep sleep until next reading
  Serial.println("Entering deep sleep...");
  powerManager.enterDeepSleep(SLEEP_MIN_SECONDS, SLEEP_MAX_SECONDS);
}

void performSensorCycle() {
  Serial.println("\n--- Starting Sensor Cycle ---");

  // Allow sensor to warm up
  delay(SENSOR_WARMUP_DELAY_MS);

  // Read sensor data
  SensorReading currentReading = readSensorData();

  if (!currentReading.valid) {
    Serial.println("Failed to read sensor data, will retry next cycle");
    return;
  }

  ReportDecision decision = reporting.analyzeReading(currentReading);
  dataManager.storeReading(currentReading);

  if (decision.shouldReport || DEBUG_ENABLED) {
    Serial.printf("Reporting needed: %s\n", decision.reasonText.c_str());

    // Execute the report
    bool reportSuccess = reporting.executeReport(currentReading, decision);

    if (reportSuccess) {
      Serial.println("Report sent successfully");
    } else {
      Serial.println("Report failed");
    }

    // Disconnect to save power
    connectivity.disconnect();
  } else {
    Serial.println("No reporting needed this cycle");
  }

  // Print current status
  printSystemStatus(currentReading);
}

SensorReading readSensorData() {
  SensorReading reading;

  Serial.println("Reading sensor data...");

  // Read TDS and temperature
  TDS_Sensor_UART::TDS_Data data = tds_sensor.read_tds_and_temp();

  if (data.valid) {
    reading.tds = data.tds;
    reading.temperature = data.temp;
    reading.timestamp = millis();
    reading.valid = true;

    Serial.printf("Sensor reading: TDS=%d ppm, Temp=%.2f°C\n",
                  reading.tds, reading.temperature);
  } else {
    Serial.println("Sensor reading failed");
    reading.valid = false;
  }

  return reading;
}

void printSystemStatus(const SensorReading& reading) {
  Serial.println("\n--- System Status ---");
  Serial.printf("Current reading: TDS=%d ppm, Temp=%.2f°C\n",
                reading.tds, reading.temperature);

  SensorReading lastReading = dataManager.getLastReading();
  if (lastReading.valid) {
    Serial.printf("Previous reading: TDS=%d ppm, Temp=%.2f°C\n",
                  lastReading.tds, lastReading.temperature);
  }

  Serial.printf("Free heap: %u bytes\n", ESP.getFreeHeap());
  Serial.printf("Uptime: %lu ms\n", millis());

  reporting.printReportingStats();
  Serial.println("--------------------");
}

void enterErrorSleep() {
  Serial.println("Entering error sleep mode...");
  Serial.flush();

  // Sleep for minimum time on error
  powerManager.enterDeepSleep(SLEEP_MIN_SECONDS, SLEEP_MIN_SECONDS);
}

void enterPermanentSleep() {
  Serial.println("=== ENTERING PERMANENT SLEEP MODE ===");
  Serial.println("Device will not wake up automatically.");
  Serial.println("Reset device to restart.");
  Serial.flush();

  // Disable all wake-up sources and enter deep sleep permanently
  esp_sleep_disable_wakeup_source(ESP_SLEEP_WAKEUP_ALL);
  esp_deep_sleep_start();
}

bool initializeWiFiConnection() {
  // Check if we're waking up from deep sleep
  bool isWakeup = !powerManager.isFirstBoot();

  if (isWakeup) {
    // Load WiFi failure count from NVS
    Preferences prefs;
    prefs.begin("wifi_status", false);
    wifiFailureCount = prefs.getInt("failure_count", 0);
    prefs.end();

    Serial.printf("Wake-up detected. WiFi failure count: %d\n", wifiFailureCount);

    if (wifiFailureCount >= MAX_WIFI_FAILURES) {
      Serial.println("Maximum WiFi failures reached. Entering permanent sleep.");
      return false;
    }
  }

  // Try to load saved credentials
  WiFiCredentials creds = btConfig.loadCredentials();

  if (!creds.valid) {
    Serial.println("No valid WiFi credentials found.");

    if (isWakeup) {
      // If waking up without credentials, enter permanent sleep
      Serial.println("No credentials on wake-up. Entering permanent sleep.");
      return false;
    }

    // First boot without credentials - start Bluetooth config
    return handleBluetoothConfig();
  }

  // Initialize connectivity with saved credentials
  if (!connectivity.begin(creds)) {
    Serial.println("Failed to initialize connectivity with saved credentials");
    return false;
  }

  // Try to connect to WiFi
  if (!connectivity.connectWiFi(WIFI_TIMEOUT_MS)) {
    wifiFailureCount++;

    // Save failure count to NVS
    Preferences prefs;
    prefs.begin("wifi_status", false);
    prefs.putInt("failure_count", wifiFailureCount);
    prefs.end();

    Serial.printf("WiFi connection failed. Failure count: %d\n", wifiFailureCount);

    if (wifiFailureCount >= MAX_WIFI_FAILURES) {
      Serial.println("Maximum WiFi failures reached.");
      return false;
    }

    return false; // Will retry on next wake-up
  }

  // WiFi connected successfully - reset failure count
  if (wifiFailureCount > 0) {
    wifiFailureCount = 0;
    Preferences prefs;
    prefs.begin("wifi_status", false);
    prefs.putInt("failure_count", 0);
    prefs.end();
    Serial.println("WiFi connected. Failure count reset.");
  }

  return true;
}

bool handleBluetoothConfig() {
  Serial.println("=== BLUETOOTH CONFIGURATION MODE ===");
  Serial.printf("Device name: %s\n", btConfig.getBluetoothName().c_str());
  Serial.println("Waiting for configuration via Bluetooth...");

  // Start Bluetooth config mode with configured timeout
  bool configSuccess = btConfig.startConfigMode(BT_CONFIG_TIMEOUT_MS);

  if (!configSuccess) {
    Serial.println("Bluetooth configuration timeout or failed.");
    return false;
  }

  Serial.println("Configuration received successfully!");

  // Load the new credentials and test connection
  WiFiCredentials creds = btConfig.loadCredentials();
  if (!creds.valid) {
    Serial.println("Invalid credentials received");
    return false;
  }

  // Initialize connectivity with new credentials
  if (!connectivity.begin(creds)) {
    Serial.println("Failed to initialize connectivity with new credentials");
    return false;
  }

  // Test WiFi connection
  if (!connectivity.connectWiFi(WIFI_TIMEOUT_MS)) {
    Serial.println("Failed to connect with new credentials");
    btConfig.clearCredentials(); // Clear invalid credentials
    return false;
  }

  Serial.println("WiFi connection successful with new credentials!");
  return true;
}

// Debug helper functions
void printMemoryInfo() {
  if (ENABLE_MEMORY_MONITORING) {
    Serial.printf("Free heap: %u bytes\n", ESP.getFreeHeap());
    Serial.printf("Largest free block: %u bytes\n", ESP.getMaxAllocHeap());
    Serial.printf("Min free heap: %u bytes\n", ESP.getMinFreeHeap());
  }
}

void printSystemInfo() {
  Serial.println("\n=== System Information ===");
  Serial.printf("Chip model: %s\n", ESP.getChipModel());
  Serial.printf("Chip revision: %d\n", ESP.getChipRevision());
  Serial.printf("CPU frequency: %d MHz\n", ESP.getCpuFreqMHz());
  Serial.printf("Flash size: %d bytes\n", ESP.getFlashChipSize());
  Serial.printf("SDK version: %s\n", ESP.getSdkVersion());
  printMemoryInfo();
  Serial.println("==========================");
}