#include <Arduino.h>
#include "TDS_Sensor_UART.h"

// --- 配置 ---
// 根据你的 ESP32-S3 接线，选择一个串口和引脚
// ESP32 通常有 3 个硬件串口: Serial, Serial1, Serial2
// Serial(0) 用于 USB 监控和上传，我们使用 Serial1
HardwareSerial& SensorSerial = Serial1;
const int RX_PIN = 8;
const int TX_PIN = 9;
const long SENSOR_BAUDRATE = 9600;

// 实例化传感器库对象，传入串口对象
TDS_Sensor_UART tds_sensor(SensorSerial);

void setup() {
  // 启动用于打印信息的监视器串口
  Serial.begin(115200);
  while (!Serial); // 等待串口连接
  Serial.println("\n--- TDS Sensor Arduino Demo ---");

  // 启动与传感器通信的串口
  // 对于 ESP32，可以在 begin 中直接指定引脚
  tds_sensor.begin(SENSOR_BAUDRATE, SERIAL_8N1, RX_PIN, TX_PIN);
  Serial.println("Sensor serial port initialized.");
  Serial.printf("Using RX=%d, TX=%d, Baud=%ld\n", RX_PIN, TX_PIN, SENSOR_BAUDRATE);

  // --- 演示设置指令 (通常只需要设置一次, 如果不确定请注释掉) ---
  /*
  Serial.println("\nAttempting to set NTC B-Value to 3435...");
  uint8_t status = tds_sensor.set_ntc_b_value(3435);
  Serial.printf("  -> Status: %s (Code: 0x%02X)\n", tds_sensor.getStatusMessage(status), status);
  delay(1000); // 等待一下
  */
}

void loop() {
  // Serial.println("\nRequesting TDS and Temperature data...");

  // 调用函数读取数据
  TDS_Sensor_UART::TDS_Data data = tds_sensor.read_tds_and_temp();

  // 检查返回的数据是否有效
  if (data.valid) {
    Serial.print("  -> Success! ");
    Serial.print("TDS: ");
    Serial.print(data.tds);
    Serial.print(" ppm, ");
    Serial.print("Temperature: ");
    Serial.print(data.temp, 2); // 保留两位小数
    Serial.println(" C");
  } else {
    Serial.println("  -> Failed to read from sensor. Check wiring or sensor power.");
  }

  // 每 2 秒读取一次
  delay(2000);
}