#include <Arduino.h>
#include "Config.h"
#include "TDS_Sensor_UART.h"
#include "SensorDataManager.h"
#include "PowerManager.h"
#include "ConnectivityManager.h"
#include "ReportingLogic.h"

// Hardware configuration
HardwareSerial& SensorSerial = Serial1;
TDS_Sensor_UART tds_sensor(SensorSerial);

// System components
SensorDataManager dataManager;
PowerManager powerManager;
ConnectivityManager connectivity;
ReportingLogic reporting(dataManager, connectivity);

// Global state
bool systemInitialized = false;
unsigned long lastSensorRead = 0;

// Function declarations
void performSensorCycle();
SensorReading readSensorData();
void printSystemStatus(const SensorReading& reading);
void enterErrorSleep();
void printMemoryInfo();
void printSystemInfo();

void setup() {
  // Initialize serial communication
  Serial.begin(SERIAL_BAUDRATE);
  while (!Serial && millis() < 5000); // Wait up to 5 seconds for serial

  Serial.println("\n=== Fish Tank Sensor System ===");
  Serial.printf("Firmware Version: %s\n", FIRMWARE_VERSION);
  Serial.printf("Device: %s\n", DEVICE_NAME);

  // Initialize power manager first to check wake-up reason
  powerManager.begin();

  // Initialize sensor data manager
  if (!dataManager.begin()) {
    Serial.println("Failed to initialize data manager!");
    enterErrorSleep();
    return;
  }

  // Initialize sensor
  tds_sensor.begin(SENSOR_BAUDRATE, SERIAL_8N1, SENSOR_RX_PIN, SENSOR_TX_PIN);
  Serial.printf("Sensor initialized on RX=%d, TX=%d, Baud=%ld\n",
                SENSOR_RX_PIN, SENSOR_TX_PIN, SENSOR_BAUDRATE);

  // Initialize connectivity (but don't connect yet)
  connectivity.begin(WIFI_SSID, WIFI_PASSWORD, MQTT_SERVER, MQTT_PORT, MQTT_USER, MQTT_PASSWORD);

  // Initialize reporting logic
  reporting.begin();

  systemInitialized = true;

  Serial.printf("System initialized. Boot count: %u\n", dataManager.getBootCount());

  if (powerManager.isFirstBoot()) {
    Serial.println("First boot detected");
    printSystemInfo();
  } else {
    Serial.println("Wake-up from deep sleep");
  }

  if (DEBUG_ENABLED) {
    printMemoryInfo();
  }
}

void loop() {
  if (!systemInitialized) {
    enterErrorSleep();
    return;
  }

  // Main sensor reading and reporting cycle
  performSensorCycle();

  // Enter deep sleep until next reading
  // Serial.println("Entering deep sleep...");
  // powerManager.enterDeepSleep(SLEEP_MIN_SECONDS, SLEEP_MAX_SECONDS);
  delay(15*1000);
}

void performSensorCycle() {
  

  // Allow sensor to warm up
  delay(SENSOR_WARMUP_DELAY_MS);
  Serial.println("\n--- Starting Sensor Cycle ---");
  // Read sensor data
  SensorReading currentReading = readSensorData();

  if (!currentReading.valid) {
    Serial.println("Failed to read sensor data, will retry next cycle");
    return;
  }

  // Store the reading
  dataManager.storeReading(currentReading);

  // Analyze if reporting is needed
  ReportDecision decision = reporting.analyzeReading(currentReading);

  if (1) {
    Serial.printf("Reporting needed: %s\n", decision.reasonText.c_str());

    // Execute the report
    bool reportSuccess = reporting.executeReport(currentReading, decision);

    if (reportSuccess) {
      Serial.println("Report sent successfully");
    } else {
      Serial.println("Report failed");
    }

    // Disconnect to save power
    connectivity.disconnect();
  } else {
    Serial.println("No reporting needed this cycle");
  }

  // Print current status
  printSystemStatus(currentReading);
}

SensorReading readSensorData() {
  SensorReading reading;

  Serial.println("Reading sensor data...");

  // Read TDS and temperature
  TDS_Sensor_UART::TDS_Data data = tds_sensor.read_tds_and_temp();

  if (data.valid) {
    reading.tds = data.tds;
    reading.temperature = data.temp;
    reading.timestamp = millis();
    reading.valid = true;

    Serial.printf("Sensor reading: TDS=%d ppm, Temp=%.2f°C\n",
                  reading.tds, reading.temperature);
  } else {
    Serial.println("Sensor reading failed");
    reading.valid = false;
  }

  return reading;
}

void printSystemStatus(const SensorReading& reading) {
  Serial.println("\n--- System Status ---");
  Serial.printf("Current reading: TDS=%d ppm, Temp=%.2f°C\n",
                reading.tds, reading.temperature);

  SensorReading lastReading = dataManager.getLastReading();
  if (lastReading.valid) {
    Serial.printf("Previous reading: TDS=%d ppm, Temp=%.2f°C\n",
                  lastReading.tds, lastReading.temperature);
  }

  Serial.printf("Free heap: %u bytes\n", ESP.getFreeHeap());
  Serial.printf("Uptime: %lu ms\n", millis());

  reporting.printReportingStats();
  Serial.println("--------------------");
}

void enterErrorSleep() {
  Serial.println("Entering error sleep mode...");
  Serial.flush();

  // Sleep for minimum time on error
  powerManager.enterDeepSleep(SLEEP_MIN_SECONDS, SLEEP_MIN_SECONDS);
}

// Debug helper functions
void printMemoryInfo() {
  if (ENABLE_MEMORY_MONITORING) {
    Serial.printf("Free heap: %u bytes\n", ESP.getFreeHeap());
    Serial.printf("Largest free block: %u bytes\n", ESP.getMaxAllocHeap());
    Serial.printf("Min free heap: %u bytes\n", ESP.getMinFreeHeap());
  }
}

void printSystemInfo() {
  Serial.println("\n=== System Information ===");
  Serial.printf("Chip model: %s\n", ESP.getChipModel());
  Serial.printf("Chip revision: %d\n", ESP.getChipRevision());
  Serial.printf("CPU frequency: %d MHz\n", ESP.getCpuFreqMHz());
  Serial.printf("Flash size: %d bytes\n", ESP.getFlashChipSize());
  Serial.printf("SDK version: %s\n", ESP.getSdkVersion());
  printMemoryInfo();
  Serial.println("==========================");
}