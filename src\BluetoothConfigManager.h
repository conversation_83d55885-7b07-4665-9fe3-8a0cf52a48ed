#ifndef BLUETOOTH_CONFIG_MANAGER_H
#define BLUETOOTH_CONFIG_MANAGER_H

#include <Arduino.h>
#include <BluetoothSerial.h>
#include <Preferences.h>
#include <ArduinoJson.h>
#include "Config.h"

struct WiFiCredentials {
    String ssid;
    String password;
    String mqttServer;
    int mqttPort;
    String mqttUser;
    String mqttPassword;
    bool valid;
    
    WiFiCredentials() : mqttPort(1883), valid(false) {}
};

class BluetoothConfigManager {
public:
    BluetoothConfigManager();
    ~BluetoothConfigManager();
    
    // Initialize Bluetooth configuration manager
    bool begin();
    
    // Load saved WiFi credentials from NVS
    WiFiCredentials loadCredentials();
    
    // Save WiFi credentials to NVS
    bool saveCredentials(const WiFiCredentials& creds);
    
    // Clear saved credentials
    void clearCredentials();
    
    // Start Bluetooth configuration mode
    bool startConfigMode(uint32_t timeoutMs = 180000); // 3 minutes default
    
    // Stop Bluetooth and cleanup
    void stop();
    
    // Check if credentials are saved and valid
    bool hasValidCredentials();
    
    // Get device Bluetooth name
    String getBluetoothName();

private:
    BluetoothSerial SerialBT;
    Preferences preferences;
    bool initialized;
    bool configModeActive;
    String deviceName;
    
    // NVS keys
    static const char* NVS_NAMESPACE;
    static const char* KEY_WIFI_SSID;
    static const char* KEY_WIFI_PASSWORD;
    static const char* KEY_MQTT_SERVER;
    static const char* KEY_MQTT_PORT;
    static const char* KEY_MQTT_USER;
    static const char* KEY_MQTT_PASSWORD;
    static const char* KEY_CONFIG_VALID;
    
    // Configuration timeout
    static const uint32_t CONFIG_TIMEOUT_MS = 180000; // 3 minutes
    
    // Generate unique device name based on MAC
    void generateDeviceName();
    
    // Handle incoming Bluetooth data
    bool handleConfigData(const String& data);
    
    // Parse JSON configuration
    bool parseConfigJson(const String& json, WiFiCredentials& creds);
    
    // Send response via Bluetooth
    void sendResponse(const String& message, bool success = true);
    
    // Send configuration instructions
    void sendInstructions();
    
    // Validate credentials format
    bool validateCredentials(const WiFiCredentials& creds);
};

#endif // BLUETOOTH_CONFIG_MANAGER_H
